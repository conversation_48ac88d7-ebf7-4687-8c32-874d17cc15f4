import streamlit as st

# Title of the app
st.title("User Information Form")

# Text input for name
name = st.text_input("Enter your name:")

# Number input for age
age = st.number_input("Enter your age:", min_value=0, max_value=120, step=1)

# Radio button for gender selection
gender = st.radio("Select your gender:", ("Male", "Female", "Other"))

# Displaying the entered information
if name:
    st.write(f"Hello **{name}**!")
    st.write(f"Age: {age}")
    st.write(f"Gender: {gender}")

