import tkinter as tk
root = tk.Tk()
root.title("My app")
root.geometry("400x400")
label = tk.Label(root, text="Hello World",font=("Arial",14),fg="red",bg="yellow")
label.pack()
def clicked():
    print("button clicked")
button = tk.Button(root, text="Click me", command=clicked)
button.pack()
def clicked2():
    print("button clicked2")
button2 = tk.But<PERSON>(root, text="Click me2", command=clicked2)
button2.pack()
msg = tk.Message(root, text="This is a message",font=("Arial",14),fg="blue",bg="green")
msg.pack()
text_box = tk.Text(root, height=5, width=30)
text_box.pack()
menu=tk.Menu(root)
root.config(menu=menu)
filemenu = tk.Menu(menu)
#menu bar
menu.add_cascade(label="File", menu=filemenu)
filemenu.add_command(label="New")
filemenu.add_command(label="Open")
filemenu.add_separator()
filemenu.add_command(label="Exit", command=root.quit)
#list box
listbox = tk.Listbox(root)
listbox=tk.Listbox(root, height=4, width=15)
listbox.insert(1, "Python")
listbox.insert(2, "Java")
listbox.insert(3, "C++")
listbox.insert(4, "C")
listbox.pack()
#spin box
spinbox = tk.Spinbox(root, from_=0, to=10)
spinbox.pack()
root.mainloop()