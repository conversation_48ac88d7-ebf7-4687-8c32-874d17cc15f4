class Bus:
    def __init__(self, bus_no, total_seats, booked_seat=0):
        self.bus_no = bus_no
        self.total_seats = total_seats
        self.booked_seat = booked_seat

    def book_seat(self, seats=1):
        if self.booked_seat + seats <= self.total_seats:
            self.booked_seat += seats
            print(f"{seats} seat(s) successfully booked.")
        else:
            print("Booking failed. Not enough seats available.")

    def cancel_seat(self, seats=1):
        if self.booked_seat >= seats:
            self.booked_seat -= seats
            print(f"{seats} seat(s) successfully cancelled.")
        else:
            print("Cancellation failed. Not enough seats booked to cancel.")

    def display_status(self):
        print(f"\nBus No: {self.bus_no}")
        print(f"Total Seats: {self.total_seats}")
        print(f"Booked Seats: {self.booked_seat}")
        print(f"Available Seats: {self.total_seats - self.booked_seat}\n")



bus_no = input("Enter Bus Number: ")
total_seats = int(input("Enter Total Seats: "))
bus = Bus(bus_no, total_seats)


while True:
    print("\n1. Book Seat")
    print("2. Cancel Seat")
    print("3. Display Status")
    print("4. Exit")

    choice = input("Enter your choice: ")

    if choice == '1':
        seats = int(input("Enter number of seats to book: "))
        bus.book_seat(seats)
    elif choice == '2':
        seats = int(input("Enter number of seats to cancel: "))
        bus.cancel_seat(seats)
    elif choice == '3':
        bus.display_status()
    elif choice == '4':
        print("Exiting the program.")
        break
    else:
        print("Invalid choice. Please try again.")

