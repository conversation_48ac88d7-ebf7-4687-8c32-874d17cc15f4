import tkinter as tk

def say_hello():
    label.config(text="Hello, Thinker")

root = tk.Tk()
root.title("Label and Button ")
root.geometry("500x300")
root.configure(bg="lightblue")

label = tk.Label(root, text="Click the button to say hello",font=("Arial", 14),fg="blue")
label.pack(pady=10)

button = tk.Button(root, text="Rohans", command=say_hello, font=("Arial",14),fg="blue",bg="red")
button.pack(pady=10)

root.mainloop()