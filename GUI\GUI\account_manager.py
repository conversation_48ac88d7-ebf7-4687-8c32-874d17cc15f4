import tkinter as tk
from tkinter import ttk, messagebox

class AccountManager:
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Account Manager")
        self.window.geometry("800x600")
        self.window.configure(bg='#f0f0f0')
        
        self.accounts = [
            {"id": "001", "name": "Checking Account", "type": "Checking", "balance": 2450.75},
            {"id": "002", "name": "Savings Account", "type": "Savings", "balance": 15230.50},
            {"id": "003", "name": "Credit Card", "type": "Credit", "balance": -1245.30},
            {"id": "004", "name": "Investment Account", "type": "Investment", "balance": 8750.25}
        ]
        
        self.create_interface()
        
    def create_interface(self):
        # Title
        title_label = tk.Label(self.window, text="Account Management", 
                              font=('Arial', 18, 'bold'), bg='#f0f0f0')
        title_label.pack(pady=20)
        
        # Button frame
        button_frame = tk.Frame(self.window, bg='#f0f0f0')
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="Add Account", command=self.add_account,
                 bg='#27ae60', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        tk.Button(button_frame, text="Edit Account", command=self.edit_account,
                 bg='#3498db', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        tk.Button(button_frame, text="Delete Account", command=self.delete_account,
                 bg='#e74c3c', fg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=5)
        
        # Account list
        self.create_account_list()
        
    def create_account_list(self):
        # Treeview for account list
        columns = ('ID', 'Name', 'Type', 'Balance')
        self.tree = ttk.Treeview(self.window, columns=columns, show='headings', height=15)
        
        # Define headings
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
        
        # Insert data
        for account in self.accounts:
            balance_color = 'red' if account['balance'] < 0 else 'black'
            self.tree.insert('', tk.END, values=(
                account['id'], 
                account['name'], 
                account['type'], 
                f"${account['balance']:,.2f}"
            ))
        
        self.tree.pack(fill='both', expand=True, padx=20, pady=20)
        
    def add_account(self):
        self.account_dialog("Add Account")
        
    def edit_account(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Selection", "Please select an account to edit")
            return
        self.account_dialog("Edit Account", selected[0])
        
    def delete_account(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Selection", "Please select an account to delete")
            return
        
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this account?"):
            self.tree.delete(selected[0])
            
    def account_dialog(self, title, item=None):
        dialog = tk.Toplevel(self.window)
        dialog.title(title)
        dialog.geometry("400x300")
        dialog.configure(bg='#f0f0f0')
        
        # Form fields
        tk.Label(dialog, text="Account Name:", bg='#f0f0f0').pack(pady=5)
        name_var = tk.StringVar()
        tk.Entry(dialog, textvariable=name_var, width=30).pack(pady=5)
        
        tk.Label(dialog, text="Account Type:", bg='#f0f0f0').pack(pady=5)
        type_var = tk.StringVar()
        type_combo = ttk.Combobox(dialog, textvariable=type_var, 
                                 values=['Checking', 'Savings', 'Credit', 'Investment'])
        type_combo.pack(pady=5)
        
        tk.Label(dialog, text="Initial Balance:", bg='#f0f0f0').pack(pady=5)
        balance_var = tk.StringVar()
        tk.Entry(dialog, textvariable=balance_var, width=30).pack(pady=5)
        
        # Buttons
        button_frame = tk.Frame(dialog, bg='#f0f0f0')
        button_frame.pack(pady=20)
        
        tk.Button(button_frame, text="Save", command=lambda: self.save_account(dialog, name_var, type_var, balance_var, item),
                 bg='#27ae60', fg='white').pack(side='left', padx=10)
        tk.Button(button_frame, text="Cancel", command=dialog.destroy,
                 bg='#95a5a6', fg='white').pack(side='left', padx=10)
        
    def save_account(self, dialog, name_var, type_var, balance_var, item=None):
        try:
            name = name_var.get()
            acc_type = type_var.get()
            balance = float(balance_var.get())
            
            if not name or not acc_type:
                messagebox.showerror("Error", "Please fill all fields")
                return
            
            if item:  # Edit existing
                self.tree.item(item, values=(
                    self.tree.item(item)['values'][0],  # Keep same ID
                    name, acc_type, f"${balance:,.2f}"
                ))
            else:  # Add new
                new_id = f"{len(self.accounts) + 1:03d}"
                self.tree.insert('', tk.END, values=(new_id, name, acc_type, f"${balance:,.2f}"))
            
            dialog.destroy()
            
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid balance amount")