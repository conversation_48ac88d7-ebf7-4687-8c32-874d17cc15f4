import tkinter as tk
from tkinter import ttk
import math

class Calculator:
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Financial Calculator")
        self.window.geometry("400x600")
        self.window.configure(bg='#f0f0f0')
        
        self.current_input = ""
        self.result_var = tk.StringVar(value="0")
        
        self.create_calculator_interface()
        
    def create_calculator_interface(self):
        # Notebook for different calculator types
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Basic Calculator Tab
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="Basic Calculator")
        self.create_basic_calculator(basic_frame)
        
        # Interest Calculator Tab
        interest_frame = ttk.Frame(notebook)
        notebook.add(interest_frame, text="Interest Calculator")
        self.create_interest_calculator(interest_frame)
        
        # Loan Calculator Tab
        loan_frame = ttk.Frame(notebook)
        notebook.add(loan_frame, text="Loan Calculator")
        self.create_loan_calculator(loan_frame)
        
    def create_basic_calculator(self, parent):
        # Display
        display = tk.Entry(parent, textvariable=self.result_var, font=('Arial', 16),
                          justify='right', state='readonly', bg='white')
        display.pack(fill='x', padx=10, pady=10)
        
        # Button frame
        button_frame = tk.Frame(parent, bg='#f0f0f0')
        button_frame.pack(fill='both', expand=True, padx=10)
        
        # Calculator buttons
        buttons = [
            ['C', '±', '%', '÷'],
            ['7', '8', '9', '×'],
            ['4', '5', '6', '-'],
            ['1', '2', '3', '+'],
            ['0', '.', '=']
        ]
        
        for i, row in enumerate(buttons):
            for j, text in enumerate(row):
                if text == '0':
                    btn = tk.Button(button_frame, text=text, font=('Arial', 14),
                                   command=lambda t=text: self.button_click(t),
                                   bg='#ecf0f1', relief='flat', bd=1)
                    btn.grid(row=i, column=j, columnspan=2, sticky='nsew', padx=1, pady=1)
                elif text == '=':
                    btn = tk.Button(button_frame, text=text, font=('Arial', 14),
                                   command=self.calculate, bg='#3498db', fg='white',
                                   relief='flat', bd=1)
                    btn.grid(row=i, column=j+1, sticky='nsew', padx=1, pady=1)
                else:
                    btn = tk.Button(button_frame, text=text, font=('Arial', 14),
                                   command=lambda t=text: self.button_click(t),
                                   bg='#ecf0f1', relief='flat', bd=1)
                    btn.grid(row=i, column=j, sticky='nsew', padx=1, pady=1)
        
        # Configure grid weights
        for i in range(5):
            button_frame.grid_rowconfigure(i, weight=1)
        for j in range(4):
            button_frame.grid_columnconfigure(j, weight=1)
            
    def create_interest_calculator(self, parent):
        # Input fields
        tk.Label(parent, text="Principal Amount:", font=('Arial', 10)).pack(pady=5)
        self.principal_var = tk.StringVar()
        tk.Entry(parent, textvariable=self.principal_var, font=('Arial', 12)).pack(pady=5)
        
        tk.Label(parent, text="Interest Rate (%):", font=('Arial', 10)).pack(pady=5)
        self.rate_var = tk.StringVar()
        tk.Entry(parent, textvariable=self.rate_var, font=('Arial', 12)).pack(pady=5)
        
        tk.Label(parent, text="Time Period (years):", font=('Arial', 10)).pack(pady=5)
        self.time_var = tk.StringVar()
        tk.Entry(parent, textvariable=self.time_var, font=('Arial', 12)).pack(pady=5)
        
        # Calculate button
        tk.Button(parent, text="Calculate Interest", command=self.calculate_interest,
                 bg='#3498db', fg='white', font=('Arial', 12)).pack(pady=20)
        
        # Result display
        self.interest_result = tk.Text(parent, height=8, font=('Arial', 10))
        self.interest_result.pack(fill='both', expand=True, padx=10, pady=10)
        
    def create_loan_calculator(self, parent):
        # Input fields
        tk.Label(parent, text="Loan Amount:", font=('Arial', 10)).pack(pady=5)
        self.loan_amount_var = tk.StringVar()
        tk.Entry(parent, textvariable=self.loan_amount_var, font=('Arial', 12)).pack(pady=5)
        
        tk.Label(parent, text="Annual Interest Rate (%):", font=('Arial', 10)).pack(pady=5)
        self.loan_rate_var = tk.StringVar()
        tk.Entry(parent, textvariable=self.loan_rate_var, font=('Arial', 12)).pack(pady=5)
        
        tk.Label(parent, text="Loan Term (years):", font=('Arial', 10)).pack(pady=5)
        self.loan_term_var = tk.StringVar()
        tk.Entry(parent, textvariable=self.loan_term_var, font=('Arial', 12)).pack(pady=5)
        
        # Calculate button
        tk.Button(parent, text="Calculate Payment", command=self.calculate_loan,
                 bg='#3498db', fg='white', font=('Arial', 12)).pack(pady=20)
        
        # Result display
        self.loan_result = tk.Text(parent, height=8, font=('Arial', 10))
        self.loan_result.pack(fill='both', expand=True, padx=10, pady=10)
    
    def button_click(self, value):
        if value == 'C':
            self.current_input = ""
            self.result_var.set("0")
        elif value == '±':
            if self.current_input and self.current_input[0] == '-':
                self.current_input = self.current_input[1:]
            else:
                self.current_input = '-' + self.current_input
            self.result_var.set(self.current_input)
        else:
            if value in ['÷', '×', '-', '+']:
                value = {'÷': '/', '×': '*'}.get(value, value)
            self.current_input += str(value)
            self.result_var.set(self.current_input)
    
    def calculate(self):
        try:
            result = eval(self.current_input.replace('×', '*').replace('÷', '/'))
            self.result_var.set(str(result))
            self.current_input = str(result)
        except:
            self.result_var.set("Error")
            self.current_input = ""
    
    def calculate_interest(self):
        try:
            principal = float(self.principal_var.get())
            rate = float(self.rate_var.get()) / 100
            time = float(self.time_var.get())
            
            simple_interest = principal * rate * time
            compound_interest = principal * (1 + rate) ** time - principal
            
            result_text = f"""Interest Calculations:
            
Principal Amount: ${principal:,.2f}
Interest Rate: {rate*100:.2f}%
Time Period: {time} years

Simple Interest: ${simple_interest:,.2f}
Final Amount (Simple): ${principal + simple_interest:,.2f}

Compound Interest: ${compound_interest:,.2f}
Final Amount (Compound): ${principal + compound_interest:,.2f}"""
            
            self.interest_result.delete(1.0, tk.END)
            self.interest_result.insert(1.0, result_text)
        except ValueError:
            self.interest_result.delete(1.0, tk.END)
            self.interest_result.insert(1.0, "Please enter valid numbers")
    
    def calculate_loan(self):
        try:
            loan_amount = float(self.loan_amount_var.get())
            annual_rate = float(self.loan_rate_var.get()) / 100
            loan_term = float(self.loan_term_var.get())
            
            monthly_rate = annual_rate / 12
            num_payments = loan_term * 12
            
            if monthly_rate == 0:
                monthly_payment = loan_amount / num_payments
            else:
                monthly_payment = loan_amount * (monthly_rate * (1 + monthly_rate) ** num_payments) / ((1 + monthly_rate) ** num_payments - 1)
            
            total_payment = monthly_payment * num_payments
            total_interest = total_payment - loan_amount
            
            result_text = f"""Loan Payment Calculation:
            
Loan Amount: ${loan_amount:,.2f}
Annual Interest Rate: {annual_rate*100:.2f}%
Loan Term: {loan_term} years

Monthly Payment: ${monthly_payment:,.2f}
Total Payment: ${total_payment:,.2f}
Total Interest: ${total_interest:,.2f}"""
            
            self.loan_result.delete(1.0, tk.END)
            self.loan_result.insert(1.0, result_text)
        except ValueError:
            self.loan_result.delete(1.0, tk.END)
            self.loan_result.insert(1.0, "Please enter valid numbers")