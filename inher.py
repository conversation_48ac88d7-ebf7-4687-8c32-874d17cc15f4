class Vehicle:
    def __init__(self,brand):
        self.brand = brand
        
    def start(self):
        print(f"{self.brand}vehicle is stating..")
        
class Car(Vehicle):
    def __init__(self,brand, model):
        super().__init__(brand)
        self.model = model
    def show_details(self):
        print(f"Brand: {self.brand}, Model: {self.model}")
my_car=Car("BMW","X9")
my_car.start()
my_car.show_details()