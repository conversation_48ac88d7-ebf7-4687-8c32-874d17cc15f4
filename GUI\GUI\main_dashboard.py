import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont
from datetime import datetime
from calculator import Calculator
from account_manager import AccountManager
from transaction_window import TransactionWindow
from reports_window import ReportsWindow

class MainDashboard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Professional Banking System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Initialize components
        self.calculator = None
        self.account_manager = None
        
        self.setup_styles()
        self.create_main_layout()
        self.create_navigation_menu()
        self.create_dashboard_content()
        
    def setup_styles(self):
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Custom styles
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f0f0f0')
        self.style.configure('Balance.TLabel', font=('Arial', 14), background='white', relief='solid')
        self.style.configure('Menu.TButton', font=('Arial', 10, 'bold'))
        
    def create_main_layout(self):
        # Header
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="Professional Banking System", 
                              font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(pady=20)
        
        # Main container
        self.main_container = tk.Frame(self.root, bg='#f0f0f0')
        self.main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
    def create_navigation_menu(self):
        nav_frame = tk.Frame(self.main_container, bg='#34495e', width=200)
        nav_frame.pack(side='left', fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)
        
        nav_title = tk.Label(nav_frame, text="Navigation", font=('Arial', 14, 'bold'), 
                            fg='white', bg='#34495e')
        nav_title.pack(pady=10)
        
        buttons = [
            ("Dashboard", self.show_dashboard),
            ("Account Manager", self.open_account_manager),
            ("Transactions", self.open_transactions),
            ("Calculator", self.open_calculator),
            ("Reports", self.open_reports),
            ("Settings", self.open_settings)
        ]
        
        for text, command in buttons:
            btn = tk.Button(nav_frame, text=text, command=command,
                           font=('Arial', 10, 'bold'), bg='#3498db', fg='white',
                           width=18, pady=5, relief='flat')
            btn.pack(pady=2, padx=10, fill='x')
            
    def create_dashboard_content(self):
        # Content area
        self.content_frame = tk.Frame(self.main_container, bg='white', relief='solid', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True)
        
        # Dashboard title
        dash_title = tk.Label(self.content_frame, text="Account Overview", 
                             font=('Arial', 18, 'bold'), bg='white')
        dash_title.pack(pady=20)
        
        # Account balances section
        self.create_balance_section()
        
        # Quick actions section
        self.create_quick_actions()
        
    def create_balance_section(self):
        balance_frame = tk.LabelFrame(self.content_frame, text="Account Balances", 
                                     font=('Arial', 12, 'bold'), bg='white')
        balance_frame.pack(fill='x', padx=20, pady=10)
        
        # Sample account data
        accounts = [
            ("Checking Account", "$2,450.75", "#27ae60"),
            ("Savings Account", "$15,230.50", "#3498db"),
            ("Credit Card", "-$1,245.30", "#e74c3c"),
            ("Investment Account", "$8,750.25", "#9b59b6")
        ]
        
        for i, (name, balance, color) in enumerate(accounts):
            account_frame = tk.Frame(balance_frame, bg='white')
            account_frame.pack(fill='x', padx=10, pady=5)
            
            name_label = tk.Label(account_frame, text=name, font=('Arial', 11), 
                                 bg='white', anchor='w')
            name_label.pack(side='left')
            
            balance_label = tk.Label(account_frame, text=balance, 
                                   font=('Arial', 11, 'bold'), fg=color, bg='white')
            balance_label.pack(side='right')
            
    def create_quick_actions(self):
        actions_frame = tk.LabelFrame(self.content_frame, text="Quick Actions", 
                                     font=('Arial', 12, 'bold'), bg='white')
        actions_frame.pack(fill='x', padx=20, pady=10)
        
        button_frame = tk.Frame(actions_frame, bg='white')
        button_frame.pack(pady=10)
        
        actions = [
            ("Transfer Funds", self.transfer_funds),
            ("Pay Bills", self.pay_bills),
            ("View Statements", self.view_statements),
            ("Budget Planner", self.open_budget_planner)
        ]
        
        for i, (text, command) in enumerate(actions):
            btn = tk.Button(button_frame, text=text, command=command,
                           font=('Arial', 10), bg='#3498db', fg='white',
                           width=15, pady=8, relief='flat')
            btn.grid(row=i//2, column=i%2, padx=10, pady=5)
    
    # Navigation methods
    def show_dashboard(self):
        pass  # Already showing dashboard
        
    def open_account_manager(self):
        if not self.account_manager:
            self.account_manager = AccountManager(self.root)
        else:
            self.account_manager.window.lift()
            
    def open_transactions(self):
        TransactionWindow(self.root)
        
    def open_calculator(self):
        if not self.calculator:
            self.calculator = Calculator(self.root)
        else:
            self.calculator.window.lift()
            
    def open_reports(self):
        ReportsWindow(self.root)
        
    def open_settings(self):
        messagebox.showinfo("Settings", "Settings window coming soon!")
        
    # Quick action methods
    def transfer_funds(self):
        messagebox.showinfo("Transfer", "Transfer funds dialog coming soon!")
        
    def pay_bills(self):
        messagebox.showinfo("Bills", "Bill payment dialog coming soon!")
        
    def view_statements(self):
        messagebox.showinfo("Statements", "Statements viewer coming soon!")
        
    def open_budget_planner(self):
        messagebox.showinfo("Budget", "Budget planner coming soon!")
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = MainDashboard()
    app.run()