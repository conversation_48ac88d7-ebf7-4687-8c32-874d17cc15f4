import re

pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"


valid_email = "<EMAIL>"
invalid_email = "example123@.com"

if re.match(pattern, valid_email):
    print(f"'{valid_email}' is a valid email.")
else:
    print(f"'{valid_email}' is NOT a valid email.")

if re.match(pattern, invalid_email):
    print(f"'{invalid_email}' is a valid email.")
else:
    print(f"'{invalid_email}' is NOT a valid email.")
