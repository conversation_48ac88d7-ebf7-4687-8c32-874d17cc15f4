import sys
from PyQt5.QtWidgets import QApplication, QWidget, QPushButton, QMessageBox

def change_to_red():
    window.setStyleSheet("background-color: red;")
    msg = QMessageBox()
    msg.setWindowTitle("Red Background")
    msg.setText("Background changed to Red!")
    msg.setIcon(QMessageBox.Information)
    msg.exec_()

def change_to_green():
    window.setStyleSheet("background-color: green;")
    msg = QMessageBox()
    msg.setWindowTitle("Green Background")
    msg.setText("Background changed to Green!")
    msg.setIcon(QMessageBox.Information)
    msg.exec_()

def change_to_blue():
    window.setStyleSheet("background-color: blue;")
    msg = QMessageBox()
    msg.setWindowTitle("Blue Background")
    msg.setText("Background changed to Blue!")
    msg.setIcon(QMessageBox.Information)
    msg.exec_()

app=QApplication(sys.argv)
window=QWidget()
window.setWindowTitle("Rohans S Martin")

red_button = QPushButton("Red",window)
red_button.move(50,100)
red_button.setStyleSheet("background-color: red; color: white; font-weight: bold;")
red_button.clicked.connect(change_to_red)

green_button = QPushButton("Green",window)
green_button.move(150,100)
green_button.setStyleSheet("background-color: green; color: white; font-weight: bold;")
green_button.clicked.connect(change_to_green)

blue_button = QPushButton("Blue",window)
blue_button.move(250,100)
blue_button.setStyleSheet("background-color: blue; color: white; font-weight: bold;")
blue_button.clicked.connect(change_to_blue)

window.resize(400,300)
window.show()
sys.exit(app.exec_())