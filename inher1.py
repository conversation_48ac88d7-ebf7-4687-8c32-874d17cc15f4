class person:
    def __init__(self, name, age):
        self.name = name
        self.age = age
    def display(self):
        print(f"Name: {self.name}, Age: {self.age}")

class student(person):
    def __init__(self, name, age, rollno):
        super().__init__(name, age)
        self.rollno = rollno
    def display(self):
        super().display()  
        print(f"Roll No: {self.rollno}")

s = student("<PERSON><PERSON><PERSON>", 21, 20)
s.display()
