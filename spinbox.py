import tkinter as tk
def show_square():
    num = int(spin.get())
    square = num * num
    result_label.config(text=f"Square of {num} is : {square}")

root = tk.Tk()
root.title("Spinbox Example")
root.geometry("300x200")

tk.Label(root, text="Select a number:").pack(pady=10)
spin = tk.Spinbox(root, from_=1, to=10)
spin.pack()

button = tk.Button(root, text="Show Square", command=show_square)
button.pack(pady=10)

result_label = tk.Label(root, text="", font=("Arial", 10, "bold"))
result_label.pack()

root.mainloop()