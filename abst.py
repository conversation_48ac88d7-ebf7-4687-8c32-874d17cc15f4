from abc import ABC, abstractmethod
class Shape(ABC):
    
    def area(self):
        pass
    def area(self,r):
        pass
    def ans(self):
        print("finding area")
    
class rec(Shape):
    def area(self,l,b):
        return l*b
class cir(Shape):
    def area(self,r):
        return 3.14*r*r
    
rect=rec()
circ=cir()

rect.ans()
print(f'rectangle:{rect.area(5,4)}')
print(f'circle:{circ.area(3)}')