import cv2
import numpy as np
import random

class SimpleEmotionDetector:
    def __init__(self):
        # Define emotion labels
        self.emotions = ['Happy', 'Sad', 'Angry', 'Surprise', 'Neutral', 'Fear']
        
        # Load face cascade classifier
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Load eye cascade for better emotion detection
        self.eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        
        # Load smile cascade
        self.smile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
    
    def analyze_facial_features(self, face_gray, face_color):
        """Analyze facial features to determine emotion"""
        h, w = face_gray.shape
        
        # Detect eyes
        eyes = self.eye_cascade.detectMultiScale(face_gray, 1.1, 5)
        
        # Detect smile
        smiles = self.smile_cascade.detectMultiScale(face_gray, 1.8, 20)
        
        # Calculate brightness and contrast
        brightness = np.mean(face_gray)
        contrast = np.std(face_gray)
        
        # Simple emotion logic based on detected features
        if len(smiles) > 0:
            return "Happy", 0.85
        elif len(eyes) == 0:
            return "Sad", 0.70
        elif brightness < 80:
            return "Sad", 0.65
        elif contrast > 50:
            return "Surprise", 0.60
        elif brightness > 130:
            return "Happy", 0.75
        else:
            return "Neutral", 0.80
    
    def get_emotion_color(self, emotion):
        """Get color for each emotion"""
        color_map = {
            'Happy': (0, 255, 0),      # Green
            'Sad': (255, 0, 0),        # Blue
            'Angry': (0, 0, 255),      # Red
            'Surprise': (255, 255, 0), # Cyan
            'Fear': (128, 0, 128),     # Purple
            'Neutral': (128, 128, 128) # Gray
        }
        return color_map.get(emotion, (255, 255, 255))
    
    def detect_emotions(self):
        """Main function to detect emotions from camera feed"""
        # Initialize camera
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("Error: Could not open camera")
            return
        
        print("Simple Emotion Detection Started!")
        print("Press 'q' to quit")
        print("Press 's' to save screenshot")
        
        frame_count = 0
        
        while True:
            # Capture frame from camera
            ret, frame = cap.read()
            if not ret:
                print("Error: Could not read frame")
                break
            
            frame_count += 1
            
            # Flip frame horizontally for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(50, 50)
            )
            
            # Add frame counter
            cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Process each detected face
            for (x, y, w, h) in faces:
                # Extract face regions
                face_gray = gray[y:y+h, x:x+w]
                face_color = frame[y:y+h, x:x+w]
                
                # Analyze facial features for emotion
                emotion, confidence = self.analyze_facial_features(face_gray, face_color)
                
                # Get color for emotion
                color = self.get_emotion_color(emotion)
                
                # Draw rectangle around face
                cv2.rectangle(frame, (x, y), (x+w, y+h), color, 3)
                
                # Create emotion label with confidence
                label_text = f"{emotion}: {confidence:.2f}"
                
                # Add background rectangle for text
                text_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                cv2.rectangle(frame, (x, y-35), (x + text_size[0] + 10, y), color, -1)
                
                # Display emotion label
                cv2.putText(frame, label_text, (x + 5, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                
                # Add emotion icon/symbol
                self.draw_emotion_icon(frame, emotion, x + w + 10, y + h//2)
            
            # Add instructions
            cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, frame.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Display the frame
            cv2.imshow('Simple Emotion Detection', frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save screenshot
                filename = f"emotion_screenshot_{frame_count}.jpg"
                cv2.imwrite(filename, frame)
                print(f"Screenshot saved as {filename}")
        
        # Clean up
        cap.release()
        cv2.destroyAllWindows()
        print("Emotion detection stopped.")
    
    def draw_emotion_icon(self, frame, emotion, x, y):
        """Draw simple emotion icons"""
        if emotion == "Happy":
            # Draw smiley face
            cv2.circle(frame, (x, y), 20, (0, 255, 0), 2)
            cv2.circle(frame, (x-8, y-5), 3, (0, 255, 0), -1)  # Left eye
            cv2.circle(frame, (x+8, y-5), 3, (0, 255, 0), -1)  # Right eye
            cv2.ellipse(frame, (x, y+5), (10, 8), 0, 0, 180, (0, 255, 0), 2)  # Smile
        elif emotion == "Sad":
            # Draw sad face
            cv2.circle(frame, (x, y), 20, (255, 0, 0), 2)
            cv2.circle(frame, (x-8, y-5), 3, (255, 0, 0), -1)  # Left eye
            cv2.circle(frame, (x+8, y-5), 3, (255, 0, 0), -1)  # Right eye
            cv2.ellipse(frame, (x, y+15), (10, 8), 0, 180, 360, (255, 0, 0), 2)  # Frown
        elif emotion == "Angry":
            # Draw angry face
            cv2.circle(frame, (x, y), 20, (0, 0, 255), 2)
            cv2.line(frame, (x-15, y-10), (x-5, y-5), (0, 0, 255), 2)  # Left eyebrow
            cv2.line(frame, (x+5, y-5), (x+15, y-10), (0, 0, 255), 2)  # Right eyebrow
            cv2.line(frame, (x-10, y+10), (x+10, y+10), (0, 0, 255), 2)  # Mouth

def main():
    """Main function to run simple emotion detection"""
    print("=" * 50)
    print("SIMPLE EMOTION DETECTION SYSTEM")
    print("=" * 50)
    print("This system uses basic computer vision techniques")
    print("to detect emotions from facial features.")
    print("Make sure your camera is connected and working.")
    print("=" * 50)
    
    # Create emotion detector instance
    detector = SimpleEmotionDetector()
    
    # Start emotion detection
    detector.detect_emotions()

if __name__ == "__main__":
    main()
