import cv2
import numpy as np
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing.image import img_to_array
import os

class EmotionDetector:
    def __init__(self):
        # Define emotion labels
        self.emotion_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Sad', 'Surprise', 'Neutral']
        
        # Load face cascade classifier
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Load pre-trained emotion detection model
        # You can download this model from: https://github.com/oarriaga/face_classification
        self.model_path = 'emotion_model.h5'
        
        try:
            self.emotion_model = load_model(self.model_path)
            print("Emotion model loaded successfully!")
        except:
            print("Error: Could not load emotion model. Please download the model file.")
            print("You can use a simple alternative method without deep learning.")
            self.emotion_model = None
    
    def preprocess_face(self, face):
        """Preprocess face for emotion prediction"""
        face = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)
        face = cv2.resize(face, (48, 48))
        face = face.astype("float") / 255.0
        face = img_to_array(face)
        face = np.expand_dims(face, axis=0)
        return face
    
    def detect_emotion_simple(self, face):
        """Simple emotion detection based on facial features (fallback method)"""
        # Convert to grayscale
        gray_face = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)
        
        # Simple heuristic based on face brightness and contrast
        mean_brightness = np.mean(gray_face)
        std_brightness = np.std(gray_face)
        
        # Very basic emotion classification (not accurate, just for demonstration)
        if mean_brightness > 120 and std_brightness > 30:
            return "Happy", 0.7
        elif mean_brightness < 80:
            return "Sad", 0.6
        elif std_brightness > 40:
            return "Surprise", 0.5
        else:
            return "Neutral", 0.8
    
    def detect_emotions(self):
        """Main function to detect emotions from camera feed"""
        # Initialize camera
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("Error: Could not open camera")
            return
        
        print("Press 'q' to quit")
        
        while True:
            # Capture frame from camera
            ret, frame = cap.read()
            if not ret:
                print("Error: Could not read frame")
                break
            
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(30, 30)
            )
            
            # Process each detected face
            for (x, y, w, h) in faces:
                # Extract face region
                face_roi = frame[y:y+h, x:x+w]
                
                # Predict emotion
                if self.emotion_model is not None:
                    # Use deep learning model
                    processed_face = self.preprocess_face(face_roi)
                    emotion_prediction = self.emotion_model.predict(processed_face)[0]
                    emotion_label = self.emotion_labels[np.argmax(emotion_prediction)]
                    confidence = np.max(emotion_prediction)
                else:
                    # Use simple fallback method
                    emotion_label, confidence = self.detect_emotion_simple(face_roi)
                
                # Draw rectangle around face
                cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 0, 0), 2)
                
                # Display emotion label and confidence
                label_text = f"{emotion_label}: {confidence:.2f}"
                cv2.putText(frame, label_text, (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
                
                # Add emotion-specific color coding
                color = self.get_emotion_color(emotion_label)
                cv2.rectangle(frame, (x, y), (x+w, y+h), color, 3)
            
            # Display the frame
            cv2.imshow('Emotion Detection', frame)
            
            # Check for quit key
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # Clean up
        cap.release()
        cv2.destroyAllWindows()
    
    def get_emotion_color(self, emotion):
        """Get color for each emotion"""
        color_map = {
            'Happy': (0, 255, 0),      # Green
            'Sad': (255, 0, 0),        # Blue
            'Angry': (0, 0, 255),      # Red
            'Surprise': (255, 255, 0), # Cyan
            'Fear': (128, 0, 128),     # Purple
            'Disgust': (0, 128, 128),  # Olive
            'Neutral': (128, 128, 128) # Gray
        }
        return color_map.get(emotion, (255, 255, 255))

def main():
    """Main function to run emotion detection"""
    print("Starting Emotion Detection System...")
    print("Make sure your camera is connected and working.")
    
    # Create emotion detector instance
    detector = EmotionDetector()
    
    # Start emotion detection
    detector.detect_emotions()

if __name__ == "__main__":
    main()
