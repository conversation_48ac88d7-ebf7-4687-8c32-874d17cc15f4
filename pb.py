class item:
    def __init__(self,item_name,quantity,price_per_unit):
        self.item_name=item_name
        self.quantity=quantity
        self.price_per_unit=price_per_unit
        
    def cal(self):
        print("Price",self.quantity*self.price_per_unit)
        
name=input("enter the name:")
quantity=int(input("enter the quantity"))
price=int(input("Enter the price"))
item1=item(name,quantity,price)
item1.cal()
        
        