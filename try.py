try:
    file=open("example.txt","r")
    content = file.read()
except FileNotFoundError:
    print("The fielwas not found")
except IOError:
    print("An I/O error occured while reading the file")
else:
    print("File read successfully.content")
    print(content)
finally:
    try:
        file.close()
        print("file closed")
    except NameError:
        print("file was never opened so it cannot be closed")