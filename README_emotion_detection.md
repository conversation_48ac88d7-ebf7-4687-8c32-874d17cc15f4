# Human Emotion Detection using Camera

This project contains Python scripts for detecting human emotions using a camera feed.

## Files Created:

1. **emotion_detection.py** - Advanced emotion detection using deep learning
2. **simple_emotion_detection.py** - Simple emotion detection using basic computer vision
3. **requirements.txt** - Required Python packages

## Installation Steps:

### Step 1: Install Required Packages
```bash
pip install opencv-python numpy tensorflow pillow
```

Or use the requirements file:
```bash
pip install -r requirements.txt
```

### Step 2: Run the Simple Version (Recommended for beginners)
```bash
python simple_emotion_detection.py
```

### Step 3: For Advanced Version (Optional)
The advanced version requires a pre-trained emotion detection model. You can:
1. Download a pre-trained model from GitHub repositories
2. Train your own model using emotion datasets
3. Use the simple version which works without external models

## Features:

### Simple Emotion Detection:
- ✅ Works without external model files
- ✅ Real-time face detection
- ✅ Basic emotion classification (Happy, Sad, Angry, Surprise, Neutral, Fear)
- ✅ Color-coded emotion display
- ✅ Emotion icons
- ✅ Screenshot saving functionality
- ✅ Mirror effect for better user experience

### Advanced Emotion Detection:
- 🔧 Requires pre-trained model
- 🔧 More accurate emotion recognition
- 🔧 Uses deep learning techniques
- 🔧 Better confidence scores

## Controls:
- **'q'** - Quit the application
- **'s'** - Save screenshot (simple version only)

## Emotions Detected:
1. **Happy** 😊 - Green color
2. **Sad** 😢 - Blue color  
3. **Angry** 😠 - Red color
4. **Surprise** 😲 - Cyan color
5. **Fear** 😨 - Purple color
6. **Neutral** 😐 - Gray color

## Troubleshooting:

### Camera Issues:
- Make sure your camera is connected
- Close other applications using the camera
- Try changing camera index in `cv2.VideoCapture(0)` to `cv2.VideoCapture(1)`

### Installation Issues:
- Make sure you have Python 3.7+ installed
- Use virtual environment for clean installation
- On some systems, you might need to install additional packages

### Performance Issues:
- Ensure good lighting for better face detection
- Keep face within camera frame
- Avoid multiple faces for better accuracy

## How It Works:

### Simple Version:
1. Captures video from camera
2. Detects faces using Haar Cascade classifiers
3. Analyzes facial features (eyes, smile, brightness)
4. Classifies emotion based on detected features
5. Displays results with color coding and icons

### Advanced Version:
1. Uses pre-trained deep learning model
2. Preprocesses face images for neural network
3. Predicts emotions with higher accuracy
4. Displays confidence scores

## Next Steps:
- Try the simple version first
- Experiment with different lighting conditions
- Consider training custom models for better accuracy
- Add more emotion categories
- Implement emotion history tracking
