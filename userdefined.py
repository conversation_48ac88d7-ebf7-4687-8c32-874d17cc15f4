class CustomError(Exception):
    """Base class fro the exepection"""
    pass
class ValueTooSmallError(CustomError):
    """Raised when the input value is too small"""
    pass
class ValueTooLargeError(CustomError):
    """Rasied when the input is too large"""
    pass
try:
    value=int(input("enter the number"))
    if value<10:
        raise ValueTooSmallError("the value is too small")
    elif value>100:
        raise ValueTooLargeError("the value is too lareg")
except ValueTooSmallError as e:
    print(e)
except ValueTooLargeError as e:
    print(e)