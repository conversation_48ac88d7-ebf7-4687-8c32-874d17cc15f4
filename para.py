class rectangle:
    def __init__(self, width=0,height=0):
         if width and height:
              self.width=width
              self.height=height

         elif width:
              self.width=width
              self.height=width
         else:
              self.width=0
              self.height=0
    def area(self):
        return self.width*self.height               

    def display_info(self):
         print(f"width:{self.width},Height:{self.height},Area:{self.area()}")



r=rectangle(1,2)
r.area()
r.display_info()
